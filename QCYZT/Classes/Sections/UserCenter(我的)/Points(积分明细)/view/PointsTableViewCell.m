//
//  PointsTableViewCell.m
//  QCYZT
//
//  Created by Cursor on 2025/7/31.
//  Copyright © 2025年 Cursor. All rights reserved.
//

#import "PointsTableViewCell.h"

@interface PointsTableViewCell()
@property (strong, nonatomic) UILabel *contentLabel;
@property (strong, nonatomic) UILabel *timeLabel;
@property (strong, nonatomic) UILabel *pointsLabel; // 积分数量
@property (strong, nonatomic) UILabel *titleLabel;
@property (nonatomic, weak) UIView *sepLine;

@end

@implementation PointsTableViewCell

- (void)awakeFromNib {
    [super awakeFromNib];
    // Initialization code
}

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    
    if (self) {
        self.contentView.backgroundColor = UIColor.up_contentBgColor;
        UIView *line = [UIView new];
        [self.contentView addSubview:line];
        [line mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self);
            make.left.equalTo(@15);
            make.right.equalTo(@-15);
            make.height.equalTo(@0.5);
        }];
        self.sepLine = line;
        line.backgroundColor = UIColor.fm_sepline_color;
    }
    return self;
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];
    // Configure the view for the selected state
}

- (void)setModel:(PointsModel *)model {
    _model = model;
    
    self.contentLabel.text = model.title;
    
    if (model.points > 0) {
        if (self.type == PointsTypeIncome) {
            self.pointsLabel.text = [NSString stringWithFormat:@"+%.0f", model.points];
            self.pointsLabel.textColor = ColorWithHex(0xFF6B47); // 收入用红色
        } else if (self.type == PointsTypeExpend) {
            self.pointsLabel.text = [NSString stringWithFormat:@"-%.0f", model.points];
            self.pointsLabel.textColor = UIColor.up_textPrimaryColor; // 支出用默认色
        }
    } else {
        self.pointsLabel.text = @"0";
        self.pointsLabel.textColor = UIColor.up_textPrimaryColor;
    }

    // 时间格式化
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:[model.createTime doubleValue]/1000];
    if ([nowDate isToday]) {
        self.timeLabel.text = [NSString stringFromDate:nowDate format:@"HH:mm"];
    } else {
        if ([nowDate isThisYear]) {
            self.timeLabel.text = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm"];
        } else {
            self.timeLabel.text = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd"];
        }
    }
    
    self.titleLabel.text = model.content;
}

- (void)setIsFirstCell:(BOOL)isFirstCell {
    _isFirstCell = isFirstCell;
    
    if (isFirstCell) {
        self.sepLine.hidden = YES;
    } else {
        self.sepLine.hidden = NO;
    }
}

#pragma mark - lazy load

-(UILabel *)contentLabel {
    if (!_contentLabel) {
        _contentLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_contentLabel];
        [_contentLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self).offset(15.0);
            make.top.equalTo(self).offset(10.0);
            make.height.equalTo(@20);
            make.width.equalTo(@160.0);
        }];
        _contentLabel.textColor = UIColor.up_textPrimaryColor;
        _contentLabel.font = [UIFont systemFontOfSize:17.0];
    }
    
    return _contentLabel;
}

// 时间
-(UILabel *)timeLabel {
    if (!_timeLabel) {
        _timeLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_timeLabel];
        [_timeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.pointsLabel);
            make.top.equalTo(self.contentLabel.mas_bottom).offset(5.0);
            make.height.equalTo(@10);
        }];
        _timeLabel.textColor = UIColor.up_textSecondaryColor;
        _timeLabel.font = [UIFont systemFontOfSize:12.0];
    }
    
    return _timeLabel;
}

// 标题
- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_titleLabel];
        [_titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.contentLabel);
            make.top.equalTo(self.contentLabel.mas_bottom).offset(5.0);
            make.right.lessThanOrEqualTo(self.timeLabel.mas_left).offset(-15);
        }];
        _titleLabel.textColor = UIColor.up_textSecondary2Color;
        _titleLabel.font = [UIFont systemFontOfSize:12.0];
    }
    
    return _titleLabel;
}

// 积分数量
- (UILabel *)pointsLabel {
    if (!_pointsLabel) {
        _pointsLabel = [[UILabel alloc] init];
        [self.contentView addSubview:_pointsLabel];
        [_pointsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.mas_right).offset(-15.0);
            make.top.equalTo(self.contentLabel);
        }];
        _pointsLabel.textColor = UIColor.up_textPrimaryColor;
        _pointsLabel.font = [UIFont systemFontOfSize:17.0];
    }
    return _pointsLabel;
}

@end