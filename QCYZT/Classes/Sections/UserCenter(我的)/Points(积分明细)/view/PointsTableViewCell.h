//
//  PointsTableViewCell.h
//  QCYZT
//
//  Created by Cursor on 2025/7/31.
//  Copyright © 2025年 Cursor. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "PointsModel.h"

typedef NS_ENUM(NSInteger, PointsType) {
    PointsTypeExpend,
    PointsTypeIncome
};

@interface PointsTableViewCell : UITableViewCell

@property (strong, nonatomic) PointsModel *model;
@property (nonatomic, assign) PointsType type;
@property (nonatomic, assign) BOOL isFirstCell;

@end