//
//  PointsExpandViewController.m
//  QCYZT
//
//  Created by Cursor on 2025/7/31.
//  Copyright © 2025年 Cursor. All rights reserved.
//

#import "PointsExpandViewController.h"
#import "PointsModel.h"
#import "PointsTableViewCell.h"

@interface PointsExpandViewController() <UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UITableView *tableView;

@property (nonatomic, strong) NSMutableArray *dataArr;
@property (nonatomic, assign) NSUInteger page;
@property (nonatomic, assign) NSUInteger currentPage;
@property (nonatomic, assign) NSUInteger pageSize;

@end

@implementation PointsExpandViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.tableView.backgroundColor = UIColor.up_contentBgColor;
    [self setInitData];
    
    WEAKSELF
    [self.view addSubview:self.tableView];
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(__weakSelf.view);
    }];
    
    [self.tableView.mj_header beginRefreshing];
}

- (void)dealloc {
    FMLog(@"%s", __func__);
}

#pragma mark - UITableViewDelegate
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.dataArr.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    static NSString *identifier = @"pointsCell";
    PointsTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:identifier];
    if (!cell) {
        cell = [[PointsTableViewCell alloc] initWithStyle:UITableViewCellStyleDefault reuseIdentifier:identifier];
    }
    cell.type = PointsTypeExpend;
    if (indexPath.row < self.dataArr.count) {
        cell.model = [self.dataArr objectAtIndex:indexPath.row];
    }
    if (indexPath.row == 0) {
        cell.isFirstCell = YES;
    } else {
        cell.isFirstCell = NO;
    }
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:NO];
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [UIView new];
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

#pragma mark - HTTP
- (void)getMsgData {
    WEAKSELF;
    [HttpRequestTool queryConsumePage:self.page pageSize:self.pageSize start:^{
    } failure:^{
        [__weakSelf endRefreshForFailure];
        
        [SVProgressHUD showErrorWithStatus:@"网络不给力"];
    } success:^(NSDictionary *dic) {
        if ([dic[@"status"] isEqualToString:@"1"]) {
            __weakSelf.currentPage = __weakSelf.page;
            
            if (__weakSelf.page == 1) {
                [__weakSelf.tableView.mj_header endRefreshing];
                [__weakSelf.tableView.mj_footer resetNoMoreData];
                [__weakSelf.dataArr removeAllObjects];
            } else {
                [__weakSelf.tableView.mj_footer endRefreshing];
            }

            NSArray *dataArr = [NSArray modelArrayWithClass:[PointsModel class] json:dic[@"data"]];
            if (dataArr.count < self.pageSize) {
                [__weakSelf.tableView.mj_footer endRefreshingWithNoMoreData];
            }
            
            [__weakSelf.dataArr addObjectsFromArray:dataArr];
            if (!__weakSelf.dataArr.count) {
                [__weakSelf.tableView showNoDataViewWithString:@"暂无支出记录" attributes:nil position:ShowPositionCenter offsetX:0 offsetY:-30];
                __weakSelf.tableView.mj_footer.hidden = YES;
            } else {
                [__weakSelf.tableView dismissNoDataView];
                __weakSelf.tableView.mj_footer.hidden = NO;
            }
            
            [__weakSelf.tableView reloadData];
        } else {
            [__weakSelf endRefreshForFailure];
            
            [SVProgressHUD showErrorWithStatus:dic[@"errmessage"]];
        }
    }];
}

#pragma mark - Private
- (void)setInitData {
    self.page = 1;
    self.currentPage = self.page;
    self.pageSize = 20;
}

- (void)endRefreshForFailure {
    if (self.page > 1) { // 上拉失败
        [self.tableView.mj_footer endRefreshing];
    } else {
        [self.tableView.mj_header endRefreshing];
    }
    
    self.page = self.currentPage;
}

#pragma mark - Getter/Setter
- (UITableView *)tableView {
    if (!_tableView) {
        _tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain delegate:self dataSource:self viewController:self headerTarget:self headerAction:@selector(headerAction) footerTarget:self footerAction:@selector(footerAction)];
        _tableView.backgroundColor = UIColor.up_contentBgColor;
        _tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
        _tableView.rowHeight = 74.0f;
        _tableView.mj_footer.hidden = YES;
    }
    return _tableView;
}

- (void)headerAction {
    self.page = 1;
    [self getMsgData];
}

- (void)footerAction {
    self.page++;
    [self getMsgData];
}

- (NSMutableArray *)dataArr {
    if (!_dataArr) {
        _dataArr = [NSMutableArray array];
    }
    
    return _dataArr;
}

@end
