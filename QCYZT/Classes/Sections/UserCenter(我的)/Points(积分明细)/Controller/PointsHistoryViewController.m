//
//  PointsHistoryViewController.m
//  QCYZT
//
//  Created by Cursor on 2025/7/31.
//  Copyright © 2025年 Cursor. All rights reserved.
//

#import "PointsHistoryViewController.h"
#import "YTGOtherWebVC.h"

#define kHeaderHeight 60

@interface PointsHistoryViewController () <SGPageTitleViewDelegate,SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) NSArray *titleArr;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;

@property (nonatomic,strong) UIView *headerView;
@property (nonatomic,weak) UILabel *pointsLabel;

@end

@implementation PointsHistoryViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;
    self.title = @"积分明细";
    self.titleArr = @[@{@"title":@"支出", @"vc":@"PointsExpandViewController"},
                      @{@"title":@"收入", @"vc":@"PointsIncomeViewController"}];
    
    [self.view addSubview:self.headerView];

    [self setupPageView];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
    self.pointsLabel.text = [NSString stringWithFormat:@"%zd", userModel.points];
    
    [self configNavRedColor];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - Private
-(void)setupPageView {
    [self.pageTitleView removeFromSuperview];
    [self.pageContentCollectionView removeFromSuperview];
    self.pageTitleView = nil;
    self.pageContentCollectionView = nil;

    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(17.0);
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = BoldFontWithSize(17.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES;
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.fm_sepline_color;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, kHeaderHeight, UI_SCREEN_WIDTH, 45) delegate:self titleNames:[self.titleArr valueForKeyPath:@"title"] configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;;
    [self.view addSubview:self.pageTitleView];
    
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, kHeaderHeight + 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(UI_SAFEAREA_TOP_HEIGHT + 45 + kHeaderHeight + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
    
    self.pageTitleView.selectedIndex = self.index;
}

- (NSArray *)addChildVC {
    [self.titleArr enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        Class clazz = NSClassFromString([_titleArr[idx] objectForKey:@"vc"]);
        UIViewController * vc = [[clazz alloc] init];
        
        [self addChildViewController:vc];
    }];
    
    return self.childViewControllers;
}

- (void)explainBtnClick {
    WEAKSELF
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        // 积分说明
        YTGOtherWebVC *vc = [[YTGOtherWebVC alloc] init];
        vc.startPage = [NSString stringWithFormat:@"%@%@",prefix,@"/api/v2/user/pointsExplain"]; // 需要后端提供积分说明页面
        vc.titleStr = @"积分说明";
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    }];
}

#pragma mark - Getter/Setter
- (UIView *)headerView {
    if (!_headerView) {
        UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, UI_SCREEN_WIDTH, kHeaderHeight)];
        headerView.backgroundColor = UIColor.fm_nav_color;
        
        UIImageView *pointsImageV = [[UIImageView alloc] init];
        [headerView addSubview:pointsImageV];
        [pointsImageV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.centerY.equalTo(headerView);
        }];
        pointsImageV.image = [UIImage imageNamed:@"积分图标"]; // 需要添加积分图标
        
        UILabel *label = [[UILabel alloc] init];
        [headerView addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(headerView);
            make.left.equalTo(pointsImageV.mas_right).offset(5);
        }];
        label.textColor = ColorWithHex(0xf9ded9);
        label.text = @"当前积分:";
        
        UIButton *explainBtn = [[UIButton alloc] init];
        [headerView addSubview:explainBtn];
        [explainBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(headerView);
            make.right.equalTo(@(-15));
            make.width.height.equalTo(@20);
        }];
        [explainBtn setImage:[[UIImage imageNamed:@"积分解释"] imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal] forState:UIControlStateNormal]; // 需要添加积分解释图标
        [explainBtn addTarget:self action:@selector(explainBtnClick) forControlEvents:UIControlEventTouchUpInside];
        
        UILabel *pointsLabel = [[UILabel alloc] init];
        [headerView addSubview:pointsLabel];
        [pointsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(headerView);
            make.left.equalTo(label.mas_right).offset(10);
        }];
        pointsLabel.textColor = [UIColor whiteColor];
        pointsLabel.text = @"0";
        self.pointsLabel = pointsLabel;
        
        // 添加获得积分按钮（类似UI图中的"获得积分"按钮）
        UIButton *getPointsBtn = [[UIButton alloc] init];
        [headerView addSubview:getPointsBtn];
        [getPointsBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(headerView);
            make.right.equalTo(explainBtn.mas_left).offset(-15);
            make.height.equalTo(@28);
            make.width.equalTo(@80);
        }];
        [getPointsBtn setTitle:@"获得积分" forState:UIControlStateNormal];
        [getPointsBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        getPointsBtn.titleLabel.font = FontWithSize(12);
        getPointsBtn.backgroundColor = ColorWithHex(0xFF6B47);
        getPointsBtn.layer.cornerRadius = 14;
        [getPointsBtn addTarget:self action:@selector(getPointsBtnClick) forControlEvents:UIControlEventTouchUpInside];
        
        _headerView = headerView;
    }
    return _headerView;
}

- (void)getPointsBtnClick {
    // 跳转到获得积分页面或任务页面
    // 这里可以根据实际需求跳转到相应页面
}

@end