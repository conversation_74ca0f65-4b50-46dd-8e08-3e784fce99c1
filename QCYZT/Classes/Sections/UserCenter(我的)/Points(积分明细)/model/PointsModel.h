//
//  PointsModel.h
//  QCYZT
//
//  Created by Cursor on 2025/7/31.
//  Copyright © 2025年 Cursor. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface PointsModel : NSObject

@property (nonatomic, assign) NSInteger identifier; // id字段，避免关键字冲突
@property (nonatomic, assign) NSInteger type; // 积分类型
@property (nonatomic, copy) NSString *title; // 类型标题
@property (nonatomic, assign) CGFloat coin; // 金币数量
@property (nonatomic, assign) CGFloat points; // 积分数量
@property (nonatomic, copy) NSString *content; // 内容描述
@property (nonatomic, copy) NSString *createTime; // 创建时间

@end